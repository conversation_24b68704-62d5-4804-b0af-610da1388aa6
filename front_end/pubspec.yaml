name: daily_motivator
description: Daily motivational quotes with customizable notifications
publish_to: "none"
version: 1.0.0+2

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.6
  http: ^1.4.0
  provider: ^6.1.5
  dartz: ^0.10.1
  equatable: ^2.0.7
  get_it: ^8.0.3
  flutter_local_notifications: ^19.3.0
  timezone: ^0.10.1
  flutter_native_timezone: ^2.0.0
  permission_handler: ^12.0.1
  shared_preferences: ^2.5.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  intl: ^0.20.2
  url_launcher: ^6.3.1
  path_provider: ^2.1.5
  flutter_launcher_icons: ^0.14.4
  google_fonts: ^6.2.1
  flex_color_scheme: ^8.0.2
  flutter_animate: ^4.5.2
  lottie: ^3.2.0
  # rive: ^0.13.20
  flutter_staggered_animations: ^1.1.1
  shimmer: ^3.0.0
  confetti: ^0.8.0
  fl_chart: ^0.71.0 
  flutter_slidable: ^3.1.2
  card_swiper: ^3.0.1
  flutter_card_swiper: ^7.0.2
  pull_to_refresh: ^2.0.0
  liquid_pull_to_refresh: ^3.0.1
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  share_plus: ^11.0.0

  home_widget: ^0.8.0
  workmanager: ^0.7.0
 

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  mockito: ^5.4.4
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1


flutter:
  uses-material-design: true
  assets:
    - assets/images/
   
  
   



flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"
  adaptive_icon_background: "#FFFFFF"
