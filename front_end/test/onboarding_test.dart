import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:daily_motivator/presentation/controllers/onboarding_controller.dart';
import 'package:daily_motivator/presentation/screens/enhanced_onboarding_screen.dart';
import 'package:daily_motivator/presentation/providers/preferences_provider.dart';
import 'package:daily_motivator/presentation/providers/notification_provider.dart';
import 'package:daily_motivator/core/theme/app_theme.dart';

// Mock providers for testing
class MockPreferencesProvider extends ChangeNotifier {
  List<String> _selectedCategories = [];
  int _notificationCount = 2;
  List<TimeOfDay> _notificationTimes = [
    const TimeOfDay(hour: 9, minute: 0),
    const TimeOfDay(hour: 18, minute: 0),
  ];
  bool _onboardingComplete = false;

  List<String> get selectedCategories => _selectedCategories;
  int get notificationCount => _notificationCount;
  List<TimeOfDay> get notificationTimes => _notificationTimes;
  bool get onboardingComplete => _onboardingComplete;
  bool get isLoading => false;

  void toggleCategory(String category) {
    if (_selectedCategories.contains(category)) {
      _selectedCategories.remove(category);
    } else {
      _selectedCategories.add(category);
    }
    notifyListeners();
  }

  Future<void> setSelectedCategories(List<String> categories) async {
    _selectedCategories = categories;
    notifyListeners();
  }

  Future<void> setNotificationCount(int count) async {
    _notificationCount = count;
    notifyListeners();
  }

  Future<void> setNotificationTime(int index, TimeOfDay time) async {
    if (index < _notificationTimes.length) {
      _notificationTimes[index] = time;
    }
    notifyListeners();
  }

  Future<void> setNotificationTimes(List<TimeOfDay> times) async {
    _notificationTimes = times;
    notifyListeners();
  }

  Future<void> completeOnboarding() async {
    _onboardingComplete = true;
    notifyListeners();
  }

  Future<void> resetOnboarding() async {
    _onboardingComplete = false;
    notifyListeners();
  }
}

class MockNotificationProvider extends ChangeNotifier {
  bool _permissionsGranted = true;

  bool get permissionsGranted => _permissionsGranted;

  Future<void> requestPermissions() async {
    _permissionsGranted = true;
    notifyListeners();
  }

  Future<void> scheduleNotifications(List<TimeOfDay> times) async {
    // Mock implementation
  }
}

void main() {
  group('Enhanced Onboarding Tests', () {
    late MockPreferencesProvider mockPreferencesProvider;
    late MockNotificationProvider mockNotificationProvider;

    setUp(() {
      mockPreferencesProvider = MockPreferencesProvider();
      mockNotificationProvider = MockNotificationProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        home: MultiProvider(
          providers: [
            ChangeNotifierProvider<PreferencesProvider>.value(
              value: mockPreferencesProvider as PreferencesProvider,
            ),
            ChangeNotifierProvider<NotificationProvider>.value(
              value: mockNotificationProvider as NotificationProvider,
            ),
          ],
          child: const EnhancedOnboardingScreen(),
        ),
      );
    }

    testWidgets('Enhanced onboarding screen loads correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check if the progress indicator is present
      expect(find.text('Step 1 of 4'), findsOneWidget);
      
      // Check if welcome page is shown initially
      expect(find.text('Welcome to\nDaily Motivator'), findsOneWidget);
      
      // Check if navigation buttons are present
      expect(find.text('Continue'), findsOneWidget);
    });

    testWidgets('Can navigate through onboarding steps', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Start from welcome page
      expect(find.text('Welcome to\nDaily Motivator'), findsOneWidget);

      // Tap continue to go to category selection
      await tester.tap(find.text('Continue'));
      await tester.pumpAndSettle();

      // Should now be on category selection page
      expect(find.text('Choose Your Interests'), findsOneWidget);
    });

    testWidgets('Progress indicator updates correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Check initial progress (step 1 of 4)
      expect(find.text('Step 1 of 4'), findsOneWidget);
      expect(find.text('25%'), findsOneWidget);
    });

    testWidgets('Skip functionality works', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find and tap skip button
      expect(find.text('Skip for now'), findsOneWidget);
      await tester.tap(find.text('Skip for now'));
      await tester.pumpAndSettle();

      // Should show confirmation dialog
      expect(find.text('Skip Onboarding?'), findsOneWidget);
    });

    testWidgets('Category selection works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Navigate to category selection
      await tester.tap(find.text('Continue'));
      await tester.pumpAndSettle();

      // Should be on category selection page
      expect(find.text('Choose Your Interests'), findsOneWidget);
      
      // Check if categories are displayed
      expect(find.text('Motivation'), findsOneWidget);
      expect(find.text('Success'), findsOneWidget);
    });

    testWidgets('Onboarding controller initializes correctly', (WidgetTester tester) async {
      final controller = OnboardingController();
      
      // Test initial state
      expect(controller.currentStep, equals(0));
      expect(controller.isFirstStep, isTrue);
      expect(controller.isLastStep, isFalse);
      expect(controller.progress, equals(0.25)); // 1/4
      expect(controller.canProceed, isTrue);
      expect(controller.showSkipOption, isTrue);
    });

    testWidgets('Animation performance test', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Pump multiple frames to test animation performance
      for (int i = 0; i < 10; i++) {
        await tester.pump(const Duration(milliseconds: 16)); // 60fps
      }
      
      await tester.pumpAndSettle();
      
      // If we get here without timeout, animations are performing well
      expect(find.byType(EnhancedOnboardingScreen), findsOneWidget);
    });
  });
}
