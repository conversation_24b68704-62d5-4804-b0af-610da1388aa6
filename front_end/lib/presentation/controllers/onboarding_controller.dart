import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/preferences_provider.dart';
import '../providers/notification_provider.dart';

import '../../core/services/logging_service.dart';

/// Enhanced onboarding controller with fluid animations and state management
class OnboardingController extends ChangeNotifier {
  static const int totalSteps = 4;
  
  // Animation controllers
  late AnimationController _pageController;
  late AnimationController _progressController;
  late AnimationController _backgroundController;
  
  // Page controller for smooth transitions
  final PageController pageController = PageController();
  
  // State management
  int _currentStep = 0;
  bool _isLoading = false;
  bool _canProceed = true;
  bool _showSkipOption = true;
  
  // Animation states
  bool _isTransitioning = false;
  final double _transitionProgress = 0.0;
  
  // Getters
  int get currentStep => _currentStep;
  bool get isLoading => _isLoading;
  bool get canProceed => _canProceed;
  bool get showSkipOption => _showSkipOption;
  bool get isTransitioning => _isTransitioning;
  double get transitionProgress => _transitionProgress;
  double get progress => (_currentStep + 1) / totalSteps;
  bool get isFirstStep => _currentStep == 0;
  bool get isLastStep => _currentStep == totalSteps - 1;
  
  // Animation getters
  AnimationController get pageAnimationController => _pageController;
  AnimationController get progressAnimationController => _progressController;
  AnimationController get backgroundAnimationController => _backgroundController;
  
  /// Initialize the controller with animation controllers
  void initialize(TickerProvider vsync) {
    _pageController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: vsync,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: vsync,
    );
    
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: vsync,
    );
    
    // Start initial animations
    _backgroundController.forward();
    _progressController.forward();
    
    LoggingService.info('Onboarding controller initialized');
  }
  
  /// Navigate to next step with animation
  Future<void> nextStep(BuildContext context) async {
    if (_isTransitioning || !_canProceed || isLastStep) return;
    
    _setTransitioning(true);
    
    try {
      // Validate current step before proceeding
      if (!await _validateCurrentStep(context)) {
        _setTransitioning(false);
        return;
      }
      
      // Animate page transition
      await _animateToNextStep();
      
      // Update step
      _currentStep++;
      _updateStepState(context);

      // Log progress
      LoggingService.info('Onboarding step completed: ${_currentStep - 1}/$totalSteps');
      
      notifyListeners();
    } catch (e) {
      LoggingService.error('Error navigating to next step: $e');
    } finally {
      _setTransitioning(false);
    }
  }
  
  /// Navigate to previous step with animation
  Future<void> previousStep() async {
    if (_isTransitioning || isFirstStep) return;
    
    _setTransitioning(true);
    
    try {
      // Animate page transition
      await _animateToPreviousStep();
      
      // Update step
      _currentStep--;
      notifyListeners();
    } catch (e) {
      LoggingService.error('Error navigating to previous step: $e');
    } finally {
      _setTransitioning(false);
    }
  }
  
  /// Skip onboarding with confirmation
  Future<void> skipOnboarding(BuildContext context) async {
    if (_isTransitioning) return;

    final shouldSkip = await _showSkipConfirmation(context);
    if (!shouldSkip) return;

    _setLoading(true);

    try {
      // Set default preferences
      await _setDefaultPreferences(context);

      // Complete onboarding
      await _completeOnboarding(context);

      // Log skip event
      LoggingService.info('Onboarding skipped at step: $_currentStep/$totalSteps');

      // Navigate to home screen
      if (context.mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      LoggingService.error('Error skipping onboarding: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Complete onboarding process
  Future<void> completeOnboarding(BuildContext context) async {
    if (_isTransitioning) return;

    _setLoading(true);

    try {
      await _completeOnboarding(context);

      // Log completion
      LoggingService.info('Onboarding completed successfully');

      // Show completion animation then navigate
      if (context.mounted) {
        await _showCompletionAnimation(context);
        if (context.mounted) {
          Navigator.of(context).pushReplacementNamed('/home');
        }
      }
    } catch (e) {
      LoggingService.error('Error completing onboarding: $e');
      // Navigate anyway on error
      if (context.mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } finally {
      _setLoading(false);
    }
  }
  
  /// Jump to specific step (for testing or navigation)
  Future<void> jumpToStep(int step) async {
    if (_isTransitioning || step < 0 || step >= totalSteps) return;
    
    _setTransitioning(true);
    
    try {
      await pageController.animateToPage(
        step,
        duration: const Duration(milliseconds: 600),
        curve: Curves.easeInOutCubic,
      );
      
      _currentStep = step;
      notifyListeners();
    } catch (e) {
      LoggingService.error('Error jumping to step: $e');
    } finally {
      _setTransitioning(false);
    }
  }
  
  /// Update step-specific state
  void _updateStepState(BuildContext context) {
    switch (_currentStep) {
      case 0: // Welcome
        _canProceed = true;
        _showSkipOption = true;
        break;
      case 1: // Category selection
        final prefs = Provider.of<PreferencesProvider>(context, listen: false);
        _canProceed = prefs.selectedCategories.isNotEmpty;
        _showSkipOption = true;
        break;
      case 2: // Notification count
        _canProceed = true;
        _showSkipOption = true;
        break;
      case 3: // Time selection
        _canProceed = true;
        _showSkipOption = false;
        break;
    }
  }
  
  /// Validate current step before proceeding
  Future<bool> _validateCurrentStep(BuildContext context) async {
    switch (_currentStep) {
      case 1: // Category selection
        final prefs = Provider.of<PreferencesProvider>(context, listen: false);
        if (prefs.selectedCategories.isEmpty) {
          _showValidationError(context, 'Please select at least one category');
          return false;
        }
        break;
      case 2: // Notification count
        final prefs = Provider.of<PreferencesProvider>(context, listen: false);
        if (prefs.notificationCount <= 0) {
          _showValidationError(context, 'Please select notification frequency');
          return false;
        }
        break;
    }
    return true;
  }
  
  /// Show validation error with animation
  void _showValidationError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  /// Animate to next step
  Future<void> _animateToNextStep() async {
    await pageController.nextPage(
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOutCubic,
    );
    
    // Update progress animation
    await _progressController.animateTo(progress);
  }
  
  /// Animate to previous step
  Future<void> _animateToPreviousStep() async {
    await pageController.previousPage(
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOutCubic,
    );
    
    // Update progress animation
    await _progressController.animateTo(progress);
  }
  
  /// Set transitioning state
  void _setTransitioning(bool transitioning) {
    _isTransitioning = transitioning;
    notifyListeners();
  }
  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Show skip confirmation dialog
  Future<bool> _showSkipConfirmation(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Skip Onboarding?'),
        content: const Text(
          'You can always change these settings later in the app settings.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Skip'),
          ),
        ],
      ),
    ) ?? false;
  }
  
  /// Set default preferences when skipping
  Future<void> _setDefaultPreferences(BuildContext context) async {
    final prefs = Provider.of<PreferencesProvider>(context, listen: false);
    
    // Set default categories if none selected
    if (prefs.selectedCategories.isEmpty) {
      await prefs.setSelectedCategories(['motivation', 'success', 'happiness']);
    }
    
    // Set default notification settings
    if (prefs.notificationCount == 0) {
      await prefs.setNotificationCount(2);
      await prefs.setNotificationTimes([
        const TimeOfDay(hour: 9, minute: 0),
        const TimeOfDay(hour: 18, minute: 0),
      ]);
    }
  }
  
  /// Show completion animation
  Future<void> _showCompletionAnimation(BuildContext context) async {
    // For now, just show a simple dialog - can be enhanced later
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'All Set!',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your personalized motivation experience is ready!',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );

    // Auto-close after 2 seconds
    await Future.delayed(const Duration(seconds: 2));
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  /// Complete the onboarding process
  Future<void> _completeOnboarding(BuildContext context) async {
    final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);

    // Request notification permissions
    await notificationProvider.requestPermissions();

    // Schedule notifications if permissions granted
    if (notificationProvider.permissionsGranted) {
      await notificationProvider.scheduleNotifications(preferencesProvider.notificationTimes);
    }

    // Mark onboarding as complete
    await preferencesProvider.completeOnboarding();

    LoggingService.info('Onboarding completed successfully');
  }
  
  @override
  void dispose() {
    _pageController.dispose();
    _progressController.dispose();
    _backgroundController.dispose();
    pageController.dispose();
    super.dispose();
  }
}
