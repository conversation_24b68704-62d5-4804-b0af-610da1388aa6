import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/theme/app_theme.dart';

/// Celebration animation for onboarding completion
class CompletionAnimation extends StatefulWidget {
  final VoidCallback? onComplete;
  final Duration duration;

  const CompletionAnimation({
    super.key,
    this.onComplete,
    this.duration = const Duration(milliseconds: 3000),
  });

  @override
  State<CompletionAnimation> createState() => _CompletionAnimationState();
}

class _CompletionAnimationState extends State<CompletionAnimation>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _particleController;
  late AnimationController _pulseController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _particleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _mainController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _setupAnimations();
    _startAnimation();
  }

  void _setupAnimations() {
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.3, 0.9, curve: Curves.easeInOut),
    ));
    
    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimation() async {
    // Start main animation
    _mainController.forward();
    
    // Start particle animation after a delay
    await Future.delayed(const Duration(milliseconds: 800));
    _particleController.forward();
    
    // Start pulse animation
    _pulseController.repeat(reverse: true);
    
    // Complete after duration
    await Future.delayed(widget.duration);
    widget.onComplete?.call();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _particleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;
    final size = MediaQuery.of(context).size;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            customColors.primaryGradient.first.withOpacity(0.1),
            customColors.primaryGradient.last.withOpacity(0.05),
            Colors.transparent,
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background particles
          _buildBackgroundParticles(size, theme),
          
          // Main content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Success icon with animations
                _buildSuccessIcon(theme, customColors),
                
                const SizedBox(height: 48),
                
                // Success message
                _buildSuccessMessage(theme),
                
                const SizedBox(height: 24),
                
                // Subtitle
                _buildSubtitle(theme),
              ],
            ),
          ),
          
          // Floating celebration elements
          _buildCelebrationElements(size, theme),
        ],
      ),
    );
  }

  Widget _buildBackgroundParticles(Size size, ThemeData theme) {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return Stack(
          children: List.generate(20, (index) {
            final progress = _particleAnimation.value;
            final delay = index * 0.05;
            final adjustedProgress = (progress - delay).clamp(0.0, 1.0);
            
            final startX = size.width * 0.5;
            final startY = size.height * 0.5;
            final endX = startX + (index % 2 == 0 ? 1 : -1) * 
                        (50 + index * 10) * adjustedProgress;
            final endY = startY - (100 + index * 5) * adjustedProgress;
            
            return Positioned(
              left: endX,
              top: endY,
              child: Opacity(
                opacity: (1.0 - adjustedProgress).clamp(0.0, 1.0),
                child: Container(
                  width: 4 + (index % 3) * 2,
                  height: 4 + (index % 3) * 2,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(0.6),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  Widget _buildSuccessIcon(ThemeData theme, CustomColors customColors) {
    return AnimatedBuilder(
      animation: Listenable.merge([_mainController, _pulseController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * _pulseAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 0.1,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: customColors.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: customColors.primaryGradient.first.withOpacity(0.4),
                    blurRadius: 30,
                    spreadRadius: 5,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                Icons.check,
                size: 60,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    )
    .animate(delay: 300.ms)
    .shimmer(duration: 1500.ms, color: Colors.white.withOpacity(0.3));
  }

  Widget _buildSuccessMessage(ThemeData theme) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Text(
            'All Set!',
            style: theme.textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    )
    .animate(delay: 800.ms)
    .fadeIn(duration: 600.ms)
    .slideY(begin: 0.3, end: 0);
  }

  Widget _buildSubtitle(ThemeData theme) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Text(
            'Your personalized motivation experience\nis ready to inspire you every day!',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    )
    .animate(delay: 1000.ms)
    .fadeIn(duration: 600.ms)
    .slideY(begin: 0.3, end: 0);
  }

  Widget _buildCelebrationElements(Size size, ThemeData theme) {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // Confetti-like elements
            ...List.generate(15, (index) {
              final progress = _particleAnimation.value;
              final delay = index * 0.1;
              final adjustedProgress = (progress - delay).clamp(0.0, 1.0);
              
              final startX = size.width * 0.5;
              final startY = size.height * 0.3;
              final endX = startX + (index % 2 == 0 ? 1 : -1) * 
                          (100 + index * 15) * adjustedProgress;
              final endY = startY + (200 + index * 10) * adjustedProgress;
              
              return Positioned(
                left: endX,
                top: endY,
                child: Transform.rotate(
                  angle: adjustedProgress * 6.28 * (index % 2 == 0 ? 1 : -1),
                  child: Opacity(
                    opacity: (1.0 - adjustedProgress * 0.8).clamp(0.0, 1.0),
                    child: Container(
                      width: 8,
                      height: 16,
                      decoration: BoxDecoration(
                        color: _getConfettiColor(index, theme),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
              );
            }),
            
            // Star elements
            ...List.generate(8, (index) {
              final progress = _particleAnimation.value;
              final delay = index * 0.15;
              final adjustedProgress = (progress - delay).clamp(0.0, 1.0);
              
              final angle = (index / 8) * 6.28;
              final radius = 80 + adjustedProgress * 60;
              final centerX = size.width * 0.5;
              final centerY = size.height * 0.4;
              
              final x = centerX + radius * math.cos(angle);
              final y = centerY + radius * math.sin(angle);
              
              return Positioned(
                left: x,
                top: y,
                child: Transform.scale(
                  scale: adjustedProgress,
                  child: Opacity(
                    opacity: (1.0 - adjustedProgress * 0.5).clamp(0.0, 1.0),
                    child: Icon(
                      Icons.star,
                      size: 16 + (index % 3) * 4,
                      color: theme.colorScheme.primary.withOpacity(0.7),
                    ),
                  ),
                ),
              );
            }),
          ],
        );
      },
    );
  }

  Color _getConfettiColor(int index, ThemeData theme) {
    final colors = [
      theme.colorScheme.primary,
      theme.colorScheme.secondary,
      theme.customColors.success,
      theme.customColors.warning,
      theme.customColors.info,
    ];
    return colors[index % colors.length];
  }
}


