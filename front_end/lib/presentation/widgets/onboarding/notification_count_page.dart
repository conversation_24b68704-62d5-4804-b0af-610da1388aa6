import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../providers/preferences_provider.dart';
import '../../../core/theme/app_theme.dart';

/// Enhanced notification count selection page with animations
class NotificationCountPage extends StatefulWidget {
  final bool isActive;
  final VoidCallback? onSelectionChanged;

  const NotificationCountPage({
    super.key,
    this.isActive = false,
    this.onSelectionChanged,
  });

  @override
  State<NotificationCountPage> createState() => _NotificationCountPageState();
}

class _NotificationCountPageState extends State<NotificationCountPage>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _optionsController;
  late AnimationController _selectionController;
  
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _headerSlideAnimation;
  late Animation<double> _optionsFadeAnimation;


  final List<Map<String, dynamic>> _notificationOptions = [
    {
      'count': 1,
      'title': 'Once Daily',
      'subtitle': 'Perfect for a gentle daily reminder',
      'icon': Icons.looks_one,
      'color': Colors.green,
      'description': 'One motivational quote to start or end your day',
    },
    {
      'count': 2,
      'title': 'Twice Daily',
      'subtitle': 'Morning motivation and evening reflection',
      'icon': Icons.looks_two,
      'color': Colors.blue,
      'description': 'Balanced motivation throughout your day',
    },
    {
      'count': 3,
      'title': 'Three Times',
      'subtitle': 'Morning, afternoon, and evening inspiration',
      'icon': Icons.looks_3,
      'color': Colors.orange,
      'description': 'Consistent motivation to keep you going',
    },
    {
      'count': 4,
      'title': 'Four Times',
      'subtitle': 'Regular motivation throughout the day',
      'icon': Icons.looks_4,
      'color': Colors.purple,
      'description': 'Maximum motivation for peak performance',
    },
  ];

  @override
  void initState() {
    super.initState();
    
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _optionsController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _setupAnimations();
    
    if (widget.isActive) {
      _startAnimations();
    }
  }

  void _setupAnimations() {
    _headerFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOut,
    ));
    
    _headerSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic,
    ));
    
    _optionsFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _optionsController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
    ));
    

  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 100));
    _headerController.forward();
    
    await Future.delayed(const Duration(milliseconds: 200));
    _optionsController.forward();
  }

  @override
  void didUpdateWidget(NotificationCountPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.isActive != widget.isActive && widget.isActive) {
      _startAnimations();
    }
  }

  @override
  void dispose() {
    _headerController.dispose();
    _optionsController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Animated header
          _buildAnimatedHeader(theme),
          
          const SizedBox(height: 32),
          
          // Notification options
          Expanded(
            child: _buildNotificationOptions(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedHeader(ThemeData theme) {
    return AnimatedBuilder(
      animation: _headerController,
      builder: (context, child) {
        return Transform.translate(
          offset: _headerSlideAnimation.value * 50,
          child: Opacity(
            opacity: _headerFadeAnimation.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 4,
                      height: 32,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: theme.customColors.primaryGradient,
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Notification Frequency',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                )
                .animate(delay: 200.ms)
                .slideX(begin: -0.3, end: 0, duration: 600.ms),
                
                const SizedBox(height: 16),
                
                Text(
                  'How often would you like to receive motivational quotes? You can always change this later.',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    height: 1.5,
                  ),
                )
                .animate(delay: 400.ms)
                .fadeIn(duration: 600.ms)
                .slideY(begin: 0.3, end: 0),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotificationOptions(ThemeData theme) {
    return AnimatedBuilder(
      animation: _optionsController,
      builder: (context, child) {
        return Opacity(
          opacity: _optionsFadeAnimation.value,
          child: Consumer<PreferencesProvider>(
            builder: (context, preferencesProvider, child) {
              return ListView.separated(
                physics: const BouncingScrollPhysics(),
                itemCount: _notificationOptions.length,
                separatorBuilder: (context, index) => const SizedBox(height: 16),
                itemBuilder: (context, index) {
                  final option = _notificationOptions[index];
                  final isSelected = preferencesProvider.notificationCount == option['count'];
                  
                  return _AnimatedNotificationOption(
                    option: option,
                    isSelected: isSelected,
                    index: index,
                    onTap: () {
                      preferencesProvider.setNotificationCount(option['count']);
                      widget.onSelectionChanged?.call();
                      
                      // Trigger selection animation
                      _selectionController.forward().then((_) {
                        _selectionController.reverse();
                      });
                    },
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

class _AnimatedNotificationOption extends StatefulWidget {
  final Map<String, dynamic> option;
  final bool isSelected;
  final int index;
  final VoidCallback onTap;

  const _AnimatedNotificationOption({
    required this.option,
    required this.isSelected,
    required this.index,
    required this.onTap,
  });

  @override
  State<_AnimatedNotificationOption> createState() => _AnimatedNotificationOptionState();
}

class _AnimatedNotificationOptionState extends State<_AnimatedNotificationOption>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _hoverAnimation;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _hoverAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final option = widget.option;

    return AnimatedBuilder(
      animation: _hoverAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _hoverAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap,
            onTapDown: (_) => _hoverController.forward(),
            onTapUp: (_) => _hoverController.reverse(),
            onTapCancel: () => _hoverController.reverse(),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOutCubic,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: widget.isSelected
                    ? LinearGradient(
                        colors: theme.customColors.primaryGradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
                color: widget.isSelected ? null : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: widget.isSelected 
                      ? Colors.transparent
                      : theme.colorScheme.outline,
                  width: widget.isSelected ? 0 : 1,
                ),
                boxShadow: widget.isSelected ? [
                  BoxShadow(
                    color: theme.customColors.primaryGradient.first.withOpacity(0.3),
                    blurRadius: 12,
                    spreadRadius: 2,
                    offset: const Offset(0, 4),
                  ),
                ] : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Icon
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: widget.isSelected 
                          ? Colors.white.withOpacity(0.2)
                          : (option['color'] as Color).withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      option['icon'] as IconData,
                      size: 28,
                      color: widget.isSelected 
                          ? Colors.white
                          : option['color'] as Color,
                    ),
                  )
                  .animate(target: widget.isSelected ? 1 : 0)
                  .scaleXY(begin: 1.0, end: 1.1, duration: 200.ms)
                  .then()
                  .scaleXY(begin: 1.1, end: 1.0, duration: 200.ms),
                  
                  const SizedBox(width: 16),
                  
                  // Content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          option['title'] as String,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: widget.isSelected 
                                ? Colors.white
                                : theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          option['subtitle'] as String,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: widget.isSelected 
                                ? Colors.white.withOpacity(0.9)
                                : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          option['description'] as String,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: widget.isSelected 
                                ? Colors.white.withOpacity(0.8)
                                : theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Selection indicator
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: widget.isSelected 
                          ? Colors.white
                          : Colors.transparent,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: widget.isSelected 
                            ? Colors.white
                            : theme.colorScheme.outline,
                        width: 2,
                      ),
                    ),
                    child: widget.isSelected
                        ? Icon(
                            Icons.check,
                            size: 16,
                            color: theme.customColors.primaryGradient.first,
                          )
                        : null,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    )
    .animate(delay: (widget.index * 150).ms)
    .scaleXY(begin: 0.0, end: 1.0, duration: 600.ms, curve: Curves.elasticOut)
    .then()
    .shimmer(
      duration: 2000.ms,
      color: theme.colorScheme.primary.withOpacity(0.1),
    );
  }
}
