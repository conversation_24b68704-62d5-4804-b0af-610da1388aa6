import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// Enhanced page transition widget for onboarding screens
class OnboardingPageTransition extends StatefulWidget {
  final Widget child;
  final int pageIndex;
  final bool isActive;
  final bool isNext;
  final bool isPrevious;
  final double transitionProgress;
  final Duration duration;

  const OnboardingPageTransition({
    super.key,
    required this.child,
    required this.pageIndex,
    required this.isActive,
    this.isNext = false,
    this.isPrevious = false,
    this.transitionProgress = 0.0,
    this.duration = const Duration(milliseconds: 600),
  });

  @override
  State<OnboardingPageTransition> createState() => _OnboardingPageTransitionState();
}

class _OnboardingPageTransitionState extends State<OnboardingPageTransition>
    with TickerProviderStateMixin {
  late AnimationController _enterController;
  late AnimationController _exitController;
  late AnimationController _parallaxController;
  
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _parallaxAnimation;

  @override
  void initState() {
    super.initState();
    
    _enterController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _exitController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _parallaxController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _setupAnimations();
    
    if (widget.isActive) {
      _enterController.forward();
      _parallaxController.forward();
    }
  }

  void _setupAnimations() {
    // Fade animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _enterController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    ));
    
    // Slide animation based on page direction
    final slideBegin = widget.isNext 
        ? const Offset(1.0, 0.0)  // Slide from right
        : widget.isPrevious 
            ? const Offset(-1.0, 0.0)  // Slide from left
            : const Offset(0.0, 0.3);  // Slide from bottom (initial)
    
    _slideAnimation = Tween<Offset>(
      begin: slideBegin,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _enterController,
      curve: Curves.easeOutCubic,
    ));
    
    // Scale animation for subtle zoom effect
    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _enterController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));
    
    // Parallax animation for background elements
    _parallaxAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _parallaxController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(OnboardingPageTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.isActive != widget.isActive) {
      if (widget.isActive) {
        _enterController.forward();
        _parallaxController.forward();
      } else {
        _exitController.forward();
      }
    }
  }

  @override
  void dispose() {
    _enterController.dispose();
    _exitController.dispose();
    _parallaxController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _enterController,
        _exitController,
        _parallaxController,
      ]),
      builder: (context, child) {
        return Transform.translate(
          offset: _slideAnimation.value * MediaQuery.of(context).size.width,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: _buildPageContent(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPageContent() {
    return Stack(
      children: [
        // Parallax background elements
        _buildParallaxBackground(),
        
        // Main content
        widget.child,
        
        // Floating particles for visual interest
        _buildFloatingParticles(),
      ],
    );
  }

  Widget _buildParallaxBackground() {
    return AnimatedBuilder(
      animation: _parallaxAnimation,
      builder: (context, child) {
        return Positioned.fill(
          child: Transform.translate(
            offset: Offset(
              _parallaxAnimation.value * 20 * (widget.pageIndex % 2 == 0 ? 1 : -1),
              _parallaxAnimation.value * 10,
            ),
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.topRight,
                  radius: 1.5,
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.05),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFloatingParticles() {
    return Positioned.fill(
      child: IgnorePointer(
        child: Stack(
          children: List.generate(5, (index) {
            final delay = index * 200;
            final size = 4.0 + (index % 3) * 2.0;
            final left = (index * 80.0) % MediaQuery.of(context).size.width;
            final top = (index * 120.0) % MediaQuery.of(context).size.height;
            
            return Positioned(
              left: left,
              top: top,
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
              )
              .animate(delay: delay.ms)
              .fadeIn(duration: 1000.ms)
              .moveY(
                begin: 0,
                end: -50,
                duration: 3000.ms,
                curve: Curves.easeInOut,
              )
              .then()
              .fadeOut(duration: 500.ms),
            );
          }),
        ),
      ),
    );
  }
}

/// Custom page transition for PageView
class OnboardingPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final int pageIndex;
  final bool isForward;

  OnboardingPageRoute({
    required this.child,
    required this.pageIndex,
    this.isForward = true,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionDuration: const Duration(milliseconds: 600),
          reverseTransitionDuration: const Duration(milliseconds: 600),
        );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // Slide transition with fade
    final slideAnimation = Tween<Offset>(
      begin: isForward ? const Offset(1.0, 0.0) : const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: Curves.easeOutCubic,
    ));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
    ));

    final scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: Curves.easeOutCubic,
    ));

    // Exit animation for the previous page
    final exitSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: isForward ? const Offset(-0.3, 0.0) : const Offset(0.3, 0.0),
    ).animate(CurvedAnimation(
      parent: secondaryAnimation,
      curve: Curves.easeInCubic,
    ));

    final exitFadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: secondaryAnimation,
      curve: const Interval(0.2, 1.0, curve: Curves.easeIn),
    ));

    return Stack(
      children: [
        // Exiting page
        if (secondaryAnimation.value > 0)
          SlideTransition(
            position: exitSlideAnimation,
            child: FadeTransition(
              opacity: exitFadeAnimation,
              child: child,
            ),
          ),
        
        // Entering page
        SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: FadeTransition(
              opacity: fadeAnimation,
              child: child,
            ),
          ),
        ),
      ],
    );
  }
}
