import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/theme/app_theme.dart';

/// Enhanced welcome page with fluid animations and interactive elements
class WelcomePage extends StatefulWidget {
  final VoidCallback? onGetStarted;
  final bool isActive;

  const WelcomePage({
    super.key,
    this.onGetStarted,
    this.isActive = false,
  });

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage>
    with TickerProviderStateMixin {
  late AnimationController _heroController;
  late AnimationController _contentController;
  late AnimationController _floatingController;
  late AnimationController _pulseController;
  
  late Animation<double> _heroScaleAnimation;
  late Animation<double> _heroRotationAnimation;
  late Animation<Offset> _contentSlideAnimation;
  late Animation<double> _contentFadeAnimation;
  late Animation<double> _floatingAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _heroController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _floatingController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _setupAnimations();
    
    if (widget.isActive) {
      _startAnimations();
    }
  }

  void _setupAnimations() {
    _heroScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _heroController,
      curve: Curves.elasticOut,
    ));
    
    _heroRotationAnimation = Tween<double>(
      begin: -0.5,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _heroController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOutCubic),
    ));
    
    _contentSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: Curves.easeOutCubic,
    ));
    
    _contentFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
    ));
    
    _floatingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 200));
    _heroController.forward();
    
    await Future.delayed(const Duration(milliseconds: 400));
    _contentController.forward();
    
    _floatingController.repeat(reverse: true);
    _pulseController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(WelcomePage oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.isActive != widget.isActive && widget.isActive) {
      _startAnimations();
    }
  }

  @override
  void dispose() {
    _heroController.dispose();
    _contentController.dispose();
    _floatingController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;
    final size = MediaQuery.of(context).size;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            customColors.primaryGradient.first.withOpacity(0.1),
            customColors.primaryGradient.last.withOpacity(0.05),
            Colors.transparent,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Floating decorative elements
              _buildFloatingElements(size),
              
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Hero icon with animations
                    _buildHeroIcon(theme, customColors),
                    
                    const SizedBox(height: 48),
                    
                    // Animated content
                    _buildAnimatedContent(theme),
                  ],
                ),
              ),
              
              // Feature highlights
              _buildFeatureHighlights(theme),
              
              const SizedBox(height: 32),
              
              // Get started button
              _buildGetStartedButton(theme, customColors),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingElements(Size size) {
    return Stack(
      children: [
        // Floating quote icons
        ...List.generate(6, (index) {
          final delay = index * 500;
          final left = (index * 120.0) % size.width;
          final top = (index * 150.0) % (size.height * 0.6);

          return Positioned(
            left: left,
            top: top,
            child: AnimatedBuilder(
              animation: _floatingAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    0,
                    _floatingAnimation.value * 20 * (index % 2 == 0 ? 1 : -1),
                  ),
                  child: Opacity(
                    opacity: 0.1,
                    child: Icon(
                      Icons.format_quote,
                      size: 24 + (index % 3) * 8,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                );
              },
            ),
          )
          .animate(delay: delay.ms)
          .fadeIn(duration: 1000.ms)
          .rotate(begin: 0, end: 0.1, duration: 2000.ms);
        }),
      ],
    );
  }

  Widget _buildHeroIcon(ThemeData theme, CustomColors customColors) {
    return AnimatedBuilder(
      animation: Listenable.merge([_heroController, _pulseController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _heroScaleAnimation.value * _pulseAnimation.value,
          child: Transform.rotate(
            angle: _heroRotationAnimation.value,
            child: Container(
              width: 140,
              height: 140,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: customColors.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: customColors.primaryGradient.first.withOpacity(0.3),
                    blurRadius: 30,
                    spreadRadius: 5,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                Icons.auto_awesome,
                size: 70,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    )
    .animate(delay: 300.ms)
    .shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.3));
  }

  Widget _buildAnimatedContent(ThemeData theme) {
    return AnimatedBuilder(
      animation: _contentController,
      builder: (context, child) {
        return Transform.translate(
          offset: _contentSlideAnimation.value * 50,
          child: Opacity(
            opacity: _contentFadeAnimation.value,
            child: Column(
              children: [
                Text(
                  'Welcome to\nDaily Motivator',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.headlineLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    height: 1.2,
                  ),
                )
                .animate(delay: 600.ms)
                .fadeIn(duration: 800.ms)
                .slideY(begin: 0.3, end: 0),
                
                const SizedBox(height: 24),
                
                Text(
                  'Transform your mindset with personalized motivational quotes delivered at the perfect moments throughout your day.',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    height: 1.5,
                  ),
                )
                .animate(delay: 800.ms)
                .fadeIn(duration: 800.ms)
                .slideY(begin: 0.3, end: 0),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureHighlights(ThemeData theme) {
    final features = [
      {'icon': Icons.psychology, 'text': 'Personalized Content'},
      {'icon': Icons.schedule, 'text': 'Perfect Timing'},
      {'icon': Icons.offline_bolt, 'text': 'Works Offline'},
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: features.asMap().entries.map((entry) {
        final index = entry.key;
        final feature = entry.value;
        
        return Column(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: Icon(
                feature['icon'] as IconData,
                color: theme.colorScheme.primary,
                size: 28,
              ),
            )
            .animate(delay: (1000 + index * 200).ms)
            .scaleXY(begin: 0.0, end: 1.0, duration: 600.ms, curve: Curves.elasticOut)
            .then()
            .shimmer(duration: 1500.ms, color: theme.colorScheme.primary.withOpacity(0.2)),
            
            const SizedBox(height: 12),
            
            Text(
              feature['text'] as String,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            )
            .animate(delay: (1200 + index * 200).ms)
            .fadeIn(duration: 600.ms)
            .slideY(begin: 0.5, end: 0),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildGetStartedButton(ThemeData theme, CustomColors customColors) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: FilledButton(
        onPressed: widget.onGetStarted,
        style: FilledButton.styleFrom(
          backgroundColor: customColors.primaryGradient.first,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Get Started',
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              size: 20,
            ),
          ],
        ),
      ),
    )
    .animate(delay: 1600.ms)
    .scaleXY(begin: 0.8, end: 1.0, duration: 600.ms, curve: Curves.elasticOut)
    .then()
    .shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.3));
  }
}
