import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../controllers/onboarding_controller.dart';
import '../widgets/onboarding/animated_progress_indicator.dart';
import '../widgets/onboarding/onboarding_page_transition.dart';
import '../widgets/onboarding/welcome_page.dart';
import '../widgets/onboarding/category_selection_page.dart';
import '../widgets/onboarding/notification_count_page.dart';
import '../widgets/onboarding/time_selection_page.dart';
import '../../core/theme/app_theme.dart';

/// Enhanced onboarding screen with fluid animations and better UX
class EnhancedOnboardingScreen extends StatefulWidget {
  const EnhancedOnboardingScreen({super.key});

  @override
  State<EnhancedOnboardingScreen> createState() => _EnhancedOnboardingScreenState();
}

class _EnhancedOnboardingScreenState extends State<EnhancedOnboardingScreen>
    with TickerProviderStateMixin {
  late OnboardingController _controller;
  late AnimationController _backgroundController;
  late Animation<Color?> _backgroundAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = OnboardingController();
    _controller.initialize(this);
    
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _setupBackgroundAnimation();
    _backgroundController.forward();
    
    // Listen to controller changes
    _controller.addListener(_onControllerChanged);
  }

  void _setupBackgroundAnimation() {
    final theme = Theme.of(context);
    _backgroundAnimation = ColorTween(
      begin: theme.colorScheme.surface,
      end: theme.colorScheme.surface.withOpacity(0.95),
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOut,
    ));
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;

    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  customColors.primaryGradient.first.withOpacity(0.05),
                  customColors.primaryGradient.last.withOpacity(0.02),
                  _backgroundAnimation.value ?? theme.colorScheme.surface,
                ],
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  // Progress indicator
                  _buildProgressSection(theme),
                  
                  // Main content
                  Expanded(
                    child: _buildPageContent(),
                  ),
                  
                  // Navigation buttons
                  _buildNavigationSection(theme),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProgressSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: AnimatedProgressIndicator(
        progress: _controller.progress,
        currentStep: _controller.currentStep,
        totalSteps: OnboardingController.totalSteps,
        showStepNumbers: true,
        primaryColor: theme.customColors.primaryGradient.first,
      ),
    );
  }

  Widget _buildPageContent() {
    return PageView.builder(
      controller: _controller.pageController,
      onPageChanged: (index) {
        // Handle page changes if needed
      },
      itemCount: OnboardingController.totalSteps,
      itemBuilder: (context, index) {
        return OnboardingPageTransition(
          pageIndex: index,
          isActive: index == _controller.currentStep,
          isNext: index == _controller.currentStep + 1,
          isPrevious: index == _controller.currentStep - 1,
          transitionProgress: _controller.transitionProgress,
          child: _buildPageForIndex(index),
        );
      },
    );
  }

  Widget _buildPageForIndex(int index) {
    switch (index) {
      case 0:
        return WelcomePage(
          isActive: index == _controller.currentStep,
          onGetStarted: () => _controller.nextStep(context),
        );
      case 1:
        return CategorySelectionPage(
          isActive: index == _controller.currentStep,
          onSelectionChanged: () {
            // Trigger UI update
            setState(() {});
          },
        );
      case 2:
        return NotificationCountPage(
          isActive: index == _controller.currentStep,
          onSelectionChanged: () {
            // Trigger UI update
            setState(() {});
          },
        );
      case 3:
        return TimeSelectionPage(
          isActive: index == _controller.currentStep,
          onSelectionChanged: () {
            // Trigger UI update
            setState(() {});
          },
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildNavigationSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Skip button (when available)
          if (_controller.showSkipOption) ...[
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: _controller.isLoading 
                    ? null 
                    : () => _controller.skipOnboarding(context),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  'Skip for now',
                  style: TextStyle(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Main navigation buttons
          Row(
            children: [
              // Back button
              if (!_controller.isFirstStep) ...[
                Expanded(
                  child: OutlinedButton(
                    onPressed: _controller.isTransitioning 
                        ? null 
                        : () => _controller.previousStep(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.arrow_back,
                          size: 18,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Back',
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
              ],
              
              // Next/Complete button
              Expanded(
                flex: _controller.isFirstStep ? 1 : 2,
                child: FilledButton(
                  onPressed: _controller.isLoading || _controller.isTransitioning || !_controller.canProceed
                      ? null
                      : () {
                          HapticFeedback.lightImpact();
                          if (_controller.isLastStep) {
                            _controller.completeOnboarding(context);
                          } else {
                            _controller.nextStep(context);
                          }
                        },
                  style: FilledButton.styleFrom(
                    backgroundColor: theme.customColors.primaryGradient.first,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _controller.isLoading
                      ? SizedBox(
                          height: 20,
                          width: 20,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _controller.isLastStep ? 'Complete Setup' : 'Continue',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              _controller.isLastStep ? Icons.check : Icons.arrow_forward,
                              size: 18,
                              color: Colors.white,
                            ),
                          ],
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}


