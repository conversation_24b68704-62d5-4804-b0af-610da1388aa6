import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/utils/platform_utils.dart';
import '../../core/services/backup_service.dart';
import '../../core/services/logging_service.dart';
import '../../core/services/storage_service.dart';
import '../providers/preferences_provider.dart';
import '../providers/notification_provider.dart'; 
import '../widgets/category_selection_widget.dart';
import '../widgets/notification_count_widget.dart';
import '../widgets/time_selection_widget.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isUpdatingNotifications = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        centerTitle: true,
      ),
      body: Consumer2<PreferencesProvider, NotificationProvider>(
        builder: (context, preferencesProvider, notificationProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildCategoriesSection(),
              const SizedBox(height: 24),
              _buildNotificationSection(),
              const SizedBox(height: 24),
              _buildTimeSection(),
              const SizedBox(height: 24),
              _buildUpdateNotificationsButton(preferencesProvider, notificationProvider),
              const SizedBox(height: 24),
              if (PlatformUtils.isLinux) _buildNotificationStatusSection(notificationProvider),
              if (PlatformUtils.isLinux) const SizedBox(height: 24),
              _buildDataManagementSection(),
              const SizedBox(height: 24),
              _buildOnboardingSection(),
              const SizedBox(height: 24),
              _buildAboutSection(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Quote Categories',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select the types of quotes you want to receive:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            const CategorySelectionWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Daily Notifications',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose how many motivational quotes you want per day:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            const NotificationCountWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Times',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Set specific times or let us choose random times for you:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            const TimeSelectionWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateNotificationsButton(
    PreferencesProvider preferencesProvider,
    NotificationProvider notificationProvider,
  ) {
    return SizedBox(
      width: double.infinity,
      child: FilledButton.icon(
        onPressed: _isUpdatingNotifications
            ? null
            : () => _updateNotifications(preferencesProvider, notificationProvider),
        icon: _isUpdatingNotifications
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.notifications_active),
        label: Text(_isUpdatingNotifications ? 'Updating...' : 'Update Notifications'),
      ),
    );
  }

  Future<void> _updateNotifications(
    PreferencesProvider preferencesProvider,
    NotificationProvider notificationProvider,
  ) async {
    setState(() {
      _isUpdatingNotifications = true;
    });

    try {
      if (!notificationProvider.permissionsGranted) {
        final granted = await notificationProvider.requestPermissions();
        if (!granted) {
          throw Exception('Notification permissions are required');
        }
      }

      await notificationProvider.scheduleNotifications(
        preferencesProvider.notificationTimes,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notifications updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update notifications: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isUpdatingNotifications = false;
      });
    }
  }

  Widget _buildNotificationStatusSection(NotificationProvider notificationProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification System Status',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check if notifications are working properly on your Linux system:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => notificationProvider.showNotificationSetupDialog(context),
                    icon: const Icon(Icons.settings),
                    label: const Text('Check Status'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final success = await notificationProvider.testNotification();
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              success
                                ? 'Test notification sent!'
                                : 'Notification test failed. Check your setup.',
                            ),
                            backgroundColor: success ? Colors.green : Colors.red,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.notifications_active),
                    label: const Text('Test'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagementSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Management',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createBackup,
                    icon: const Icon(Icons.backup),
                    label: const Text('Create Backup'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _restoreBackup,
                    icon: const Icon(Icons.restore),
                    label: const Text('Restore'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _exportData,
                    icon: const Icon(Icons.download),
                    label: const Text('Export Data'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _showStorageInfo,
                    icon: const Icon(Icons.storage),
                    label: const Text('Storage Info'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            OutlinedButton.icon(
              onPressed: _resetAllData,
              icon: const Icon(Icons.delete_forever),
              label: const Text('Reset All Data'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createBackup() async {
    if (!mounted) return;

    try {
      _showLoadingDialog('Creating backup...');

      final result = await BackupService.createBackup();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
      }

      if (result.isSuccess && result.backup != null) {
        await BackupService.shareBackup(result.backup!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Backup created and shared successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }

        LoggingService.logUserAction('create_backup_success');
      } else {
        if (mounted) {
          _showErrorDialog('Backup Failed', result.error ?? 'Unknown error occurred');
        }
        LoggingService.logUserAction('create_backup_failed', data: {'error': result.error});
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        _showErrorDialog('Backup Failed', e.toString());
      }
      LoggingService.error('Backup creation failed', error: e);
    }
  }

  Future<void> _restoreBackup() async {
    // For now, show a dialog explaining how to restore
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Restore Backup'),
        content: const Text(
          'To restore a backup:\n\n'
          '1. Make sure you have a backup file\n'
          '2. Contact support for assistance\n'
          '3. Or use the import feature when available\n\n'
          'This feature will be enhanced in future updates.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportData() async {
    if (!mounted) return;

    try {
      _showLoadingDialog('Exporting data...');

      final jsonData = await StorageService.exportData();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Data Exported'),
            content: Text(
              'Data exported successfully!\n\n'
              'Size: ${(jsonData.length / 1024).toStringAsFixed(1)} KB\n\n'
              'The data has been prepared for export. '
              'In a full implementation, this would be saved to a file or shared.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }

      LoggingService.logUserAction('export_data_success');
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        _showErrorDialog('Export Failed', e.toString());
      }
      LoggingService.error('Data export failed', error: e);
    }
  }

  void _showStorageInfo() {
    final stats = StorageService.getStorageStats();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Information'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Platform: ${stats['platform'] ?? 'Unknown'}'),
              const SizedBox(height: 8),
              Text('Total Keys: ${stats['totalKeys'] ?? 0}'),
              const SizedBox(height: 8),
              Text('Total Size: ${stats['totalSize'] ?? 0} bytes'),
              const SizedBox(height: 16),
              const Text(
                'Key Details:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              if (stats['keyStats'] != null)
                ...((stats['keyStats'] as Map<String, dynamic>).entries.map((entry) =>
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text('${entry.key}: ${entry.value} bytes'),
                  ),
                )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset All Data'),
        content: const Text(
          'This will permanently delete all your data including:\n\n'
          '• Reading history and statistics\n'
          '• Achievements and progress\n'
          '• Preferences and settings\n'
          '• User profile information\n\n'
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reset All Data'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        _showLoadingDialog('Resetting data...');

        final success = await StorageService.clearAllData();

        if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog

          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('All data has been reset successfully!'),
                backgroundColor: Colors.green,
              ),
            );

            LoggingService.logUserAction('reset_all_data_success');

            // Restart the app or navigate to onboarding
            Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
          } else {
            _showErrorDialog('Reset Failed', 'Failed to reset data. Please try again.');
          }
        }
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          _showErrorDialog('Reset Failed', e.toString());
        }
        LoggingService.error('Data reset failed', error: e);
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const ListTile(
              leading: Icon(Icons.info_outline),
              title: Text('Version'),
              subtitle: Text('1.0.0'),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.api),
              title: const Text('Quote Source'),
              subtitle: const Text('ZenQuotes.io'),
              contentPadding: EdgeInsets.zero,
              onTap: () => _launchUrl('https://zenquotes.io'),
            ),
            const ListTile(
              leading: Icon(Icons.code),
              title: Text('Open Source'),
              subtitle: Text('Built with Flutter'),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.star_rate),
              title: const Text('Rate This App'),
              subtitle: const Text('Help us improve'),
              contentPadding: EdgeInsets.zero,
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Thank you for your support!'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Onboarding',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('Reset Onboarding'),
              subtitle: const Text('Go through the setup process again'),
              contentPadding: EdgeInsets.zero,
              onTap: () => _resetOnboarding(),
            ),
            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('Onboarding Help'),
              subtitle: const Text('Learn about app features'),
              contentPadding: EdgeInsets.zero,
              onTap: () => _showOnboardingHelp(),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _resetOnboarding() async {
    final shouldReset = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Onboarding'),
        content: const Text(
          'This will reset your onboarding status and take you through the setup process again. Your current settings will be preserved.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (shouldReset == true && mounted) {
      try {
        final preferencesProvider = Provider.of<PreferencesProvider>(context, listen: false);
        await preferencesProvider.resetOnboarding();

        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/onboarding');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error resetting onboarding: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  void _showOnboardingHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Onboarding Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'The onboarding process helps you:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Choose your favorite quote categories'),
              Text('• Set notification frequency'),
              Text('• Configure notification times'),
              Text('• Personalize your experience'),
              SizedBox(height: 16),
              Text(
                'You can change these settings anytime in the Settings screen.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }
}
