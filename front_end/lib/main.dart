import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:flutter_native_timezone/flutter_native_timezone.dart';

import 'core/di/injection_container.dart';
import 'core/services/storage_service.dart';
import 'core/services/logging_service.dart';
import 'core/services/api_service.dart';
import 'core/services/offline_sync_service.dart';
import 'core/services/widget_service.dart';
import 'presentation/providers/preferences_provider.dart';
import 'presentation/providers/theme_provider.dart';
import 'presentation/providers/auth_provider.dart';
import 'presentation/screens/splash_screen.dart';
import 'presentation/screens/enhanced_onboarding_screen.dart';
import 'presentation/screens/home_screen.dart';
import 'presentation/screens/auth_screen.dart';
import 'presentation/widgets/animated_loading_widget.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize production services
  await _initializeServices();

  // Initialize dependency injection
  final di = InjectionContainer();
  await di.init();

  runApp(DailyMotivatorApp(di: di));
}

/// Initialize all production services
Future<void> _initializeServices() async {
  try {
    // Initialize timezone database
    tz.initializeTimeZones();

    // Use a more robust timezone detection approach
    String timeZoneName = 'UTC'; // Default fallback

    try {
      // Try to get local timezone from native plugin
      timeZoneName = await FlutterNativeTimezone.getLocalTimezone();
      debugPrint('Successfully got timezone: $timeZoneName');
    } catch (e) {
      debugPrint('Native timezone plugin failed: $e');

      // Fallback: Try to detect timezone from DateTime
      try {
        final now = DateTime.now();
        final utcNow = now.toUtc();
        final offset = now.difference(utcNow);

        // Map common offsets to timezone names
        final offsetHours = offset.inHours;
        timeZoneName = _getTimezoneFromOffset(offsetHours);
        debugPrint('Using timezone from offset: $timeZoneName (offset: ${offsetHours}h)');
      } catch (e2) {
        debugPrint('Offset detection failed: $e2, using UTC');
        timeZoneName = 'UTC';
      }
    }

    // Set the timezone location with fallback
    try {
      tz.setLocalLocation(tz.getLocation(timeZoneName));
      debugPrint('Timezone set successfully: $timeZoneName');
    } catch (e) {
      debugPrint('Failed to set timezone $timeZoneName: $e, falling back to UTC');
      try {
        tz.setLocalLocation(tz.getLocation('UTC'));
      } catch (e2) {
        debugPrint('Even UTC failed: $e2, using default location');
        // Use default location if even UTC fails
      }
    }

    // Initialize storage service FIRST (other services depend on it)
    await StorageService.initialize();

    // Initialize logging service (depends on storage)
    await LoggingService.initialize();

    // Initialize API service
    final apiService = ApiService();
    await apiService.initialize();

    // Initialize offline sync service
    final offlineService = OfflineSyncService(apiService: apiService);

    // Initialize widget service
    final widgetService = WidgetService(
      apiService: apiService,
      offlineService: offlineService,
    );
    await widgetService.initialize();

    // Perform initial sync if online
    if (await offlineService.isOnline()) {
      await offlineService.loadOfflineQuotes(count: 50, featuredOnly: true);
      await offlineService.syncCategories();
    }

    LoggingService.info('Production services initialized successfully');
  } catch (e) {
    debugPrint('Failed to initialize services: $e');
    // Continue app startup even if some services fail
  }
}

class DailyMotivatorApp extends StatefulWidget {
  final InjectionContainer di;

  const DailyMotivatorApp({super.key, required this.di});

  @override
  State<DailyMotivatorApp> createState() => _DailyMotivatorAppState();
}

class _DailyMotivatorAppState extends State<DailyMotivatorApp> {
  static const platform = MethodChannel('com.example.daily_motivator/widget');

  @override
  void initState() {
    super.initState();
    _setupMethodChannel();
  }

  void _setupMethodChannel() {
    platform.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'refreshWidget':
          await _handleWidgetRefresh();
          break;
        default:
          throw PlatformException(
            code: 'Unimplemented',
            details: 'Method ${call.method} not implemented',
          );
      }
    });
  }

  Future<void> _handleWidgetRefresh() async {
    try {
      // Get widget service from DI container
      final apiService = ApiService();
      await apiService.initialize();

      final offlineService = OfflineSyncService(apiService: apiService);
      final widgetService = WidgetService(
        apiService: apiService,
        offlineService: offlineService,
      );

      // Update widget with new quote
      await widgetService.updateWidget(forceRefresh: true);
      LoggingService.info('Widget refreshed from native intent');
    } catch (e) {
      LoggingService.error('Error refreshing widget from native: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Core application providers
        ChangeNotifierProvider.value(value: widget.di.authProvider),
        ChangeNotifierProvider.value(value: widget.di.quoteProvider),
        ChangeNotifierProvider.value(value: widget.di.preferencesProvider),
        ChangeNotifierProvider.value(value: widget.di.notificationProvider),
        ChangeNotifierProvider.value(value: widget.di.themeProvider),
        ChangeNotifierProvider.value(value: widget.di.analyticsProvider),
        ChangeNotifierProvider.value(value: widget.di.gamificationProvider),
        ChangeNotifierProvider.value(value: widget.di.userProvider),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Daily Motivator',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.materialThemeMode,
            home: const SplashScreen(),
            routes: {
              '/auth': (context) => const AuthScreen(),
              '/home': (context) => const AppRouter(),
              '/onboarding': (context) => const EnhancedOnboardingScreen(),
            },
          );
        },
      ),
    );
  }
}

class AppRouter extends StatelessWidget {
  const AppRouter({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<AuthProvider, PreferencesProvider>(
      builder: (context, authProvider, preferencesProvider, child) {
        // Show loading while checking auth state
        if (authProvider.state == AuthState.initial || preferencesProvider.isLoading) {
          return const Scaffold(
            body: OnboardingLoadingWidget(
              message: 'Loading...',
            ),
          );
        }

        // Users can access the app in guest mode or authenticated mode
        if (authProvider.canUseApp) {
          // If onboarding not complete, show onboarding
          if (!preferencesProvider.onboardingComplete) {
            return const EnhancedOnboardingScreen();
          }

          // Show home screen for both guest and authenticated users
          return const HomeScreen();
        }

        // Fallback to auth screen (should rarely happen with guest mode)
        return const AuthScreen();
      },
    );
  }
}

/// Helper function to map UTC offset to timezone name
String _getTimezoneFromOffset(int offsetHours) {
  switch (offsetHours) {
    case -12: return 'Pacific/Kwajalein';
    case -11: return 'Pacific/Midway';
    case -10: return 'Pacific/Honolulu';
    case -9: return 'America/Anchorage';
    case -8: return 'America/Los_Angeles';
    case -7: return 'America/Denver';
    case -6: return 'America/Chicago';
    case -5: return 'America/New_York';
    case -4: return 'America/Halifax';
    case -3: return 'America/Sao_Paulo';
    case -2: return 'Atlantic/South_Georgia';
    case -1: return 'Atlantic/Azores';
    case 0: return 'Europe/London';
    case 1: return 'Europe/Berlin';
    case 2: return 'Europe/Helsinki';
    case 3: return 'Europe/Moscow';
    case 4: return 'Asia/Dubai';
    case 5: return 'Asia/Karachi';
    case 6: return 'Asia/Dhaka';
    case 7: return 'Asia/Bangkok';
    case 8: return 'Asia/Shanghai';
    case 9: return 'Asia/Tokyo';
    case 10: return 'Australia/Sydney';
    case 11: return 'Pacific/Norfolk';
    case 12: return 'Pacific/Auckland';
    default: return 'UTC';
  }
}
