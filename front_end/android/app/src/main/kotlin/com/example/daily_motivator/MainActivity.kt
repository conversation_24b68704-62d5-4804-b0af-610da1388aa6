package com.example.daily_motivator

import android.content.Intent
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.daily_motivator/widget"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Handle widget refresh intent
        handleWidgetRefreshIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleWidgetRefreshIntent(intent)
    }

    private fun handleWidgetRefreshIntent(intent: Intent?) {
        if (intent?.action == "REFRESH_WIDGET_QUOTE") {
            // Send message to Flutter to refresh widget
            flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                val channel = MethodChannel(messenger, CHANNEL)
                channel.invokeMethod("refreshWidget", null)
            }
        }
    }
}
